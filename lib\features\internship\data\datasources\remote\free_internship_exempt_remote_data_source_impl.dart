/// -----
/// free_internship_exempt_remote_data_source_impl.dart
///
/// 免实习申请远程数据源实现
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'dart:async';
import 'package:get_it/get_it.dart';
import '../../../../../core/network/dio_client.dart';
import '../../../../../core/error/exceptions/server_exception.dart';
import '../../../../../core/error/exceptions/network_exception.dart';
import '../../../../../core/services/auth_expiry_service.dart';
import '../../../../../core/utils/logger.dart';
import '../../../data/models/free_internship_exempt_model.dart';
import '../../../data/models/free_internship_approval_request_model.dart';
import 'free_internship_exempt_remote_data_source.dart';

/// 免实习申请远程数据源实现
/// 
/// 实现从远程API获取免实习申请数据的具体逻辑
class FreeInternshipExemptRemoteDataSourceImpl implements FreeInternshipExemptRemoteDataSource {
  final DioClient _dioClient;

  FreeInternshipExemptRemoteDataSourceImpl(this._dioClient);

  @override
  Future<List<FreeInternshipExemptModel>> getFreeInternshipExemptList({
    required int planId,
    required int type,
  }) async {
    try {
      Logger.info('FreeInternshipExemptRemoteDataSource', '开始获取免实习申请列表 - planId: $planId, type: $type');
      
      final response = await _dioClient.get(
        'internshipservice/v1/internship/teacher/exempt/apply/list',
        queryParameters: {
          'planId': planId,
          'type': type,
        },
      );

      Logger.debug('FreeInternshipExemptRemoteDataSource', '获取免实习申请列表响应: $response');

      if (response is List) {
        final exempts = response
            .map((json) => FreeInternshipExemptModel.fromJson(json as Map<String, dynamic>))
            .toList();

        Logger.info('FreeInternshipExemptRemoteDataSource', '成功获取${exempts.length}个免实习申请');
        return exempts;
      } else {
        Logger.warning('FreeInternshipExemptRemoteDataSource', '响应数据格式不正确');
        return [];
      }
    } catch (e) {
      Logger.error('FreeInternshipExemptRemoteDataSource', '获取免实习申请列表失败: $e');

      // 检查是否是认证失效错误
      if (e is ServerException && e.message.contains('登陆信息失效')) {
        Logger.warning('FreeInternshipExemptRemoteDataSource', '检测到认证失效，触发认证失效处理');

        // 使用依赖注入容器中的AuthExpiryService实例
        final authExpiryService = GetIt.instance<AuthExpiryService>();

        // 异步处理认证失效，不阻塞当前流程
        unawaited(authExpiryService.handleAuthExpiryWithoutContext(
          message: '登录已过期，请重新登录',
        ).catchError((error) {
          Logger.error('FreeInternshipExemptRemoteDataSource', '处理认证失效时发生错误: $error');
        }));
      }

      if (e is ServerException || e is NetworkException) {
        rethrow;
      } else {
        throw ServerException('获取免实习申请列表失败: $e');
      }
    }
  }

  @override
  Future<void> approveFreeInternshipExempt(FreeInternshipApprovalRequestModel request) async {
    try {
      Logger.info('FreeInternshipExemptRemoteDataSource', '开始审批免实习申请 - ID: ${request.id}, 状态: ${request.status}');

      await _dioClient.post(
        'internshipservice/v1/internship/teacher/exempt/apply/review',
        data: request.toJson(),
      );

      Logger.info('FreeInternshipExemptRemoteDataSource', '审批免实习申请成功');
    } catch (e) {
      Logger.error('FreeInternshipExemptRemoteDataSource', '审批免实习申请失败: $e');

      // 检查是否是认证失效错误
      if (e is ServerException && e.message.contains('登陆信息失效')) {
        Logger.warning('FreeInternshipExemptRemoteDataSource', '检测到认证失效，触发认证失效处理');

        // 使用依赖注入容器中的AuthExpiryService实例
        final authExpiryService = GetIt.instance<AuthExpiryService>();

        // 异步处理认证失效，不阻塞当前流程
        unawaited(authExpiryService.handleAuthExpiryWithoutContext(
          message: '登录已过期，请重新登录',
        ).catchError((error) {
          Logger.error('FreeInternshipExemptRemoteDataSource', '处理认证失效时发生错误: $error');
        }));
      }

      if (e is ServerException || e is NetworkException) {
        rethrow;
      } else {
        throw ServerException('审批免实习申请失败: $e');
      }
    }
  }
}
