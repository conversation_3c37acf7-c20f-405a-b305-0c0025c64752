/// -----
/// free_internship_exempt_model.dart
///
/// 免实习申请数据模型类
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../../domain/entities/free_internship_exempt.dart';

/// 免实习申请数据模型类
/// 
/// 继承自实体类，添加JSON序列化功能
class FreeInternshipExemptModel extends FreeInternshipExempt {
  const FreeInternshipExemptModel({
    required super.id,
    required super.studentId,
    required super.studentName,
    required super.avatar,
    required super.planId,
    required super.reason,
    required super.fileUrl,
    required super.status,
    super.reviewId,
    super.reviewPerson,
    super.reviewRole,
    super.reviewOpinion,
    required super.createTime,
    required super.updateTime,
    required super.createPerson,
    required super.updatePerson,
    required super.dr,
  });

  /// 从JSON创建模型实例
  factory FreeInternshipExemptModel.fromJson(Map<String, dynamic> json) {
    return FreeInternshipExemptModel(
      id: json['id'] ?? 0,
      studentId: json['studentId'] ?? 0,
      studentName: json['studentName'] ?? '',
      avatar: json['avatar'] ?? '',
      planId: json['planId'] ?? 0,
      reason: json['reason'] ?? '',
      fileUrl: json['fileUrl'] ?? '',
      status: json['status'] ?? 0,
      reviewId: json['reviewId'],
      reviewPerson: json['reviewPerson'],
      reviewRole: json['reviewRole'],
      reviewOpinion: json['reviewOpinion'],
      createTime: json['createTime'] ?? 0,
      updateTime: json['updateTime'] ?? 0,
      createPerson: json['createPerson'] ?? '',
      updatePerson: json['updatePerson'] ?? '',
      dr: json['dr'] ?? 0,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'studentId': studentId,
      'studentName': studentName,
      'avatar': avatar,
      'planId': planId,
      'reason': reason,
      'fileUrl': fileUrl,
      'status': status,
      'reviewId': reviewId,
      'reviewPerson': reviewPerson,
      'reviewRole': reviewRole,
      'reviewOpinion': reviewOpinion,
      'createTime': createTime,
      'updateTime': updateTime,
      'createPerson': createPerson,
      'updatePerson': updatePerson,
      'dr': dr,
    };
  }

  /// 从实体类创建模型实例
  factory FreeInternshipExemptModel.fromEntity(FreeInternshipExempt entity) {
    return FreeInternshipExemptModel(
      id: entity.id,
      studentId: entity.studentId,
      studentName: entity.studentName,
      avatar: entity.avatar,
      planId: entity.planId,
      reason: entity.reason,
      fileUrl: entity.fileUrl,
      status: entity.status,
      reviewId: entity.reviewId,
      reviewPerson: entity.reviewPerson,
      reviewRole: entity.reviewRole,
      reviewOpinion: entity.reviewOpinion,
      createTime: entity.createTime,
      updateTime: entity.updateTime,
      createPerson: entity.createPerson,
      updatePerson: entity.updatePerson,
      dr: entity.dr,
    );
  }

  /// 转换为实体类
  FreeInternshipExempt toEntity() {
    return FreeInternshipExempt(
      id: id,
      studentId: studentId,
      studentName: studentName,
      avatar: avatar,
      planId: planId,
      reason: reason,
      fileUrl: fileUrl,
      status: status,
      reviewId: reviewId,
      reviewPerson: reviewPerson,
      reviewRole: reviewRole,
      reviewOpinion: reviewOpinion,
      createTime: createTime,
      updateTime: updateTime,
      createPerson: createPerson,
      updatePerson: updatePerson,
      dr: dr,
    );
  }
}
