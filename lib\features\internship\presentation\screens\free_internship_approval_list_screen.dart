/// -----
/// free_internship_approval_list_screen.dart
///
/// 免实习申请审批列表页面，展示待审批和已审批的免实习申请
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/approval_tab_bar.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/approval_list_item.dart';
import 'package:flutter_demo/core/common/image_viewer_screen.dart';
import 'package:flutter_demo/features/internship/presentation/screens/free_internship_approval_detail_screen.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/free_internship_exempt_list/free_internship_exempt_list_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/free_internship_exempt_list/free_internship_exempt_list_event.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/free_internship_exempt_list/free_internship_exempt_list_state.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_state.dart';
import 'package:flutter_demo/features/internship/presentation/adapters/free_internship_exempt_adapter.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class FreeInternshipApprovalListScreen extends StatefulWidget {
  const FreeInternshipApprovalListScreen({Key? key}) : super(key: key);

  @override
  State<FreeInternshipApprovalListScreen> createState() =>
      _FreeInternshipApprovalListScreenState();
}

class _FreeInternshipApprovalListScreenState
    extends State<FreeInternshipApprovalListScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late FreeInternshipExemptListBloc _pendingBloc;
  late FreeInternshipExemptListBloc _approvedBloc;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // 初始化BLoC
    _pendingBloc = GetIt.instance<FreeInternshipExemptListBloc>();
    _approvedBloc = GetIt.instance<FreeInternshipExemptListBloc>();

    // 监听全局实习计划状态变化
    _loadDataBasedOnGlobalState();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _pendingBloc.close();
    _approvedBloc.close();
    super.dispose();
  }

  /// 根据全局状态加载数据
  void _loadDataBasedOnGlobalState() {
    final planListBloc = GetIt.instance<PlanListGlobalBloc>();
    final state = planListBloc.state;

    if (state is PlanListGlobalLoadedState && state.currentPlan != null) {
      final planId = int.tryParse(state.currentPlan!.planId) ?? 0;
      _loadData(planId);
    }
  }

  /// 加载数据
  void _loadData(int planId) {
    if (planId > 0) {
      // 加载待审批数据
      _pendingBloc.add(LoadFreeInternshipExemptListEvent(
        planId: planId,
        type: 0, // 待审批
      ));

      // 加载已审批数据
      _approvedBloc.add(LoadFreeInternshipExemptListEvent(
        planId: planId,
        type: 1, // 已审批
      ));
    }
  }

  /// 刷新数据
  void _refreshData(int planId) {
    if (planId > 0) {
      // 刷新待审批数据
      _pendingBloc.add(RefreshFreeInternshipExemptListEvent(
        planId: planId,
        type: 0, // 待审批
      ));

      // 刷新已审批数据
      _approvedBloc.add(RefreshFreeInternshipExemptListEvent(
        planId: planId,
        type: 1, // 已审批
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: CustomAppBar(
        title: '免实习申请',
        centerTitle: true,
        showBackButton: true,
        actions: [
          IconButton(
            icon: Image.asset('assets/images/statistics_icon.png', width: 32.w, height: 32.h),
            onPressed: () {
              // TODO: 右侧图标点击事件
            },
          ),
        ],
      ),
      body: BlocListener<PlanListGlobalBloc, PlanListGlobalState>(
        bloc: GetIt.instance<PlanListGlobalBloc>(),
        listener: (context, state) {
          // 当全局实习计划状态变化时，重新加载数据
          if (state is PlanListGlobalLoadedState && state.currentPlan != null) {
            final planId = int.tryParse(state.currentPlan!.planId) ?? 0;
            _loadData(planId);
          }
        },
        child: Column(
          children: [
            // 课程头部 - 自动从全局状态获取实习计划数据
            const CourseHeaderSection(),

            // 标签栏
            BlocBuilder<FreeInternshipExemptListBloc, FreeInternshipExemptListState>(
              bloc: _pendingBloc,
              builder: (context, state) {
                int pendingCount = 0;
                if (state is FreeInternshipExemptListLoaded) {
                  pendingCount = state.exempts.length;
                } else if (state is FreeInternshipExemptListRefreshing) {
                  pendingCount = state.exempts.length;
                }

                return ApprovalTabBar(
                  controller: _tabController,
                  pendingCount: pendingCount,
                );
              },
            ),

            // 页面内容
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildPendingList(),
                  _buildApprovedList(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 待审批列表
  Widget _buildPendingList() {
    return BlocBuilder<FreeInternshipExemptListBloc, FreeInternshipExemptListState>(
      bloc: _pendingBloc,
      builder: (context, state) {
        if (state is FreeInternshipExemptListLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is FreeInternshipExemptListError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  state.message,
                  style: const TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => _loadDataBasedOnGlobalState(),
                  child: const Text('重试'),
                ),
              ],
            ),
          );
        } else if (state is FreeInternshipExemptListLoaded || state is FreeInternshipExemptListRefreshing) {
          final exempts = state is FreeInternshipExemptListLoaded
              ? state.exempts
              : (state as FreeInternshipExemptListRefreshing).exempts;

          if (exempts.isEmpty) {
            return const Center(
              child: Text(
                '暂无待审批的免实习申请',
                style: TextStyle(color: Colors.grey),
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () async {
              final planListBloc = GetIt.instance<PlanListGlobalBloc>();
              final planState = planListBloc.state;
              if (planState is PlanListGlobalLoadedState && planState.currentPlan != null) {
                final planId = int.tryParse(planState.currentPlan!.planId) ?? 0;
                _refreshData(planId);
              }
            },
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              itemCount: exempts.length,
              itemBuilder: (context, index) {
                final exempt = exempts[index];
                final adapter = FreeInternshipExemptAdapter(exempt);
                return ApprovalListItem(
                  item: adapter,
                  isPending: true,
                  onTap: () => _navigateToDetail(adapter),
                  onViewTap: () => _navigateToDetail(adapter),
                  contentBuilder: (item) => _buildFreeInternshipContent(item as FreeInternshipExemptAdapter),
                  attachmentBuilder: (item) => _buildAttachmentContent(item as FreeInternshipExemptAdapter),
                );
              },
            ),
          );
        }

        return const Center(child: Text('暂无数据'));
      },
    );
  }

  // 已审批列表
  Widget _buildApprovedList() {
    return BlocBuilder<FreeInternshipExemptListBloc, FreeInternshipExemptListState>(
      bloc: _approvedBloc,
      builder: (context, state) {
        if (state is FreeInternshipExemptListLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is FreeInternshipExemptListError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  state.message,
                  style: const TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => _loadDataBasedOnGlobalState(),
                  child: const Text('重试'),
                ),
              ],
            ),
          );
        } else if (state is FreeInternshipExemptListLoaded || state is FreeInternshipExemptListRefreshing) {
          final exempts = state is FreeInternshipExemptListLoaded
              ? state.exempts
              : (state as FreeInternshipExemptListRefreshing).exempts;

          if (exempts.isEmpty) {
            return const Center(
              child: Text(
                '暂无已审批的免实习申请',
                style: TextStyle(color: Colors.grey),
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () async {
              final planListBloc = GetIt.instance<PlanListGlobalBloc>();
              final planState = planListBloc.state;
              if (planState is PlanListGlobalLoadedState && planState.currentPlan != null) {
                final planId = int.tryParse(planState.currentPlan!.planId) ?? 0;
                _refreshData(planId);
              }
            },
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              itemCount: exempts.length,
              itemBuilder: (context, index) {
                final exempt = exempts[index];
                final adapter = FreeInternshipExemptAdapter(exempt);
                return ApprovalListItem(
                  item: adapter,
                  isPending: false,
                  onTap: () => _navigateToDetail(adapter),
                  onViewTap: () => _navigateToDetail(adapter),
                  contentBuilder: (item) => _buildFreeInternshipContent(item as FreeInternshipExemptAdapter),
                  attachmentBuilder: (item) => _buildAttachmentContent(item as FreeInternshipExemptAdapter),
                );
              },
            ),
          );
        }

        return const Center(child: Text('暂无数据'));
      },
    );
  }

  // 构建免实习申请内容
  Widget _buildFreeInternshipContent(FreeInternshipExemptAdapter adapter) {
    return buildInfoRow('免实习理由', adapter.exempt.reason);
  }

  // 构建信息行
  Widget buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value.isNotEmpty ? value : '无',
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }

  // 构建附件内容
  Widget _buildAttachmentContent(FreeInternshipExemptAdapter adapter) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            '证明文件',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
        ),
        Expanded(
          child: adapter.exempt.fileUrl.isNotEmpty
              ? Align(
                  alignment: Alignment.centerLeft,
                  child: InkWell(
                    onTap: () => _viewAttachment(adapter.exempt.fileUrl),
                    child: Container(
                      width: 98,
                      height: 98,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!, width: 1),
                      ),
                      clipBehavior: Clip.antiAlias,
                      child: adapter.exempt.fileUrl.startsWith('http')
                          ? Image.network(
                              adapter.exempt.fileUrl,
                              width: 98,
                              height: 98,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) => _buildFileIcon(),
                            )
                          : _buildFileIcon(),
                    ),
                  ),
                )
              : const Text(
                  '无',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
        ),
      ],
    );
  }

  // 构建文件图标
  Widget _buildFileIcon() {
    return Container(
      width: 98,
      height: 98,
      color: Colors.grey[200],
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.insert_drive_file,
              size: 36,
              color: Colors.grey,
            ),
            SizedBox(height: 4),
            Text(
              '证明文件',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.primaryColor,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  // 跳转到详情页面
  void _navigateToDetail(FreeInternshipExemptAdapter adapter) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FreeInternshipApprovalDetailScreen(
          approvalId: adapter.exempt.id.toString(),
          status: adapter.exempt.statusText,
        ),
      ),
    ).then((result) {
      // 如果返回结果为true，表示审批状态有变化，需要刷新列表
      if (result == true) {
        _loadDataBasedOnGlobalState();
      }
    });
  }

  // 查看附件
  void _viewAttachment(String fileUrl) {
    if (fileUrl.isEmpty) return;

    // 打开全屏图片查看器
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ImageViewerScreen(
          imageUrl: fileUrl,
          title: '证明文件',
          isNetworkImage: fileUrl.startsWith('http'),
        ),
      ),
    );
  }
}